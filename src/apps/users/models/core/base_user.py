from django.db import models
from django.contrib.auth.models import AbstractUser
from common.models.base_model import BaseModel
from django.utils import timezone
import uuid 

class BaseUser(AbstractUser):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    date_created = models.DateTimeField(default=timezone.now, editable=False)
    date_updated = models.DateTimeField(auto_now=True)
    date_deleted = models.DateTimeField(null=True, blank=True)

    class Meta:
        abstract = True

    def soft_delete(self):
        self.date_deleted = timezone.now()
        self.save()

    def restore(self):
        self.date_deleted = None
        self.save()